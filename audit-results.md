# Project Audit Results - Foundation

## Summary

This audit identifies unused files, functions, modules, and packages in your Foundation project.

## 🔍 Audit Methodology

-   Analyzed all import statements across Vue components, TypeScript files, and configuration files
-   Cross-referenced package.json dependencies with actual usage
-   Checked for orphaned files and unused exports
-   Verified configuration file usage

## 📦 Package Dependencies Analysis

### ✅ USED Dependencies

These packages are actively imported and used:

**Production Dependencies:**

-   `@neondatabase/serverless` - Used in `server/utils/drizzle.ts` and `server/utils/migrations.ts`
-   `@nuxt/image` - Configured in `nuxt.config.ts` modules
-   `@nuxt/ui` - Configured in `nuxt.config.ts` modules, used throughout components
-   `better-auth` - Used in `server/utils/auth.ts` and `server/utils/auth-client.ts`
-   `drizzle-orm` - Used extensively in database schema and utilities
-   `nuxt` - Core framework
-   `resend` - Used in `server/api/send.ts`
-   `vue` - Core framework
-   `vue-router` - Used in pages (index.vue, protected.vue)
-   `zod` - Used in all form validation (sign-up.vue, sign-in.vue, etc.)

**Dev Dependencies:**

-   `@iconify-json/lucide` - Used for icons (i-lucide-circle-alert in components)
-   `@nuxt/test-utils` - Used in vitest.config.ts and playwright.config.ts
-   `@playwright/test` - Used in playwright.config.ts and e2e tests
-   `@sentry/node` - Used in `server/plugins/sentry.ts`
-   `@sentry/vue` - Used in `plugins/sentry.client.ts`
-   `@types/uuid` - TypeScript types
-   `@vitest/coverage-v8` - Used in vitest.config.ts
-   `@vue/test-utils` - Used in component tests
-   `drizzle-kit` - Used in package.json scripts
-   `tsx` - Used in package.json scripts and Dockerfile
-   `vite-svg-loader` - Used in nuxt.config.ts
-   `vitest` - Used for testing

### ❌ UNUSED Dependencies

These packages are declared but not found in any imports:

**Production Dependencies:**

-   `@radix-icons/vue` - Not found in any imports (using @iconify instead)
-   `@vee-validate/zod` - Not found in any imports (using zod directly)
-   `clsx` - Only used in `lib/utils.ts` but that file is not imported anywhere
-   `pg` - Not found in any imports (using @neondatabase/serverless instead)
-   `postgres` - Not found in any imports (using @neondatabase/serverless instead)
-   `uuid` - Not found in any imports
-   `vee-validate` - Not found in any imports (using @nuxt/ui forms instead)

### ⚠️ PARTIALLY USED Dependencies

These packages have limited usage and should be reviewed:

**Production Dependencies:**

-   `@vueuse/core` - Used in `components/Header.vue` for `createReusableTemplate` only
-   `tailwind-merge` - Used in `lib/utils.ts` but that file is not imported anywhere

**Dev Dependencies:**

-   `@sentry/profiling-node` - Imported in sentry.ts but might be redundant
-   `@sentry/types` - Not directly imported
-   `@vitejs/plugin-vue` - Not found in any imports
-   `@vitest/ui` - Only used in package.json scripts
-   `dotenv` - Used in server utils but might be redundant with Nuxt's built-in env handling
-   `happy-dom` - Not explicitly configured
-   `playwright-core` - Might be redundant with @playwright/test
-   `vite` - Might be included by Nuxt automatically
-   `wait-on` - Not found in any imports

## 📁 File Analysis

### ✅ USED Files

All core application files are being used:

-   All Vue components in `/components`
-   All pages in `/pages`
-   All server API routes in `/server/api`
-   All database schema files
-   All configuration files

### ❓ POTENTIALLY UNUSED Files

**Frontend Files:**

-   `frontend/lib/utils.ts` - Contains clsx utility but may not be used
-   `frontend/components.json` - Shadcn-vue config but project uses @nuxt/ui
-   `frontend/server/db/schema.ts` - Might be redundant with schema/index.ts

**Documentation:**

-   Some docs might be outdated (need content review)

**Generated/Build Files:**

-   `frontend/coverage/` - Test coverage reports (can be regenerated)
-   `frontend/dist/` - Build output (can be regenerated)
-   `frontend/playwright-report/` - Test reports (can be regenerated)
-   `frontend/test-results/` - Test results (can be regenerated)

## 🔧 Configuration Analysis

### ✅ USED Configurations

-   `nuxt.config.ts` - Core Nuxt configuration
-   `vitest.config.ts` - Test configuration
-   `playwright.config.ts` - E2E test configuration
-   `package.json` files - Package management
-   Docker files - Deployment
-   `.gitignore` files - Version control

### ❓ POTENTIALLY REDUNDANT Configurations

-   `frontend/components.json` - Shadcn-vue config, but project uses @nuxt/ui
-   `frontend/nuxt.config.e2e.ts` - Not found, might be referenced somewhere
-   Multiple `.gitignore` files - Could be consolidated

## 🎯 Recommendations

### High Priority - Safe to Remove

1. **Unused Dependencies:**

    ```bash
    npm uninstall @radix-icons/vue @vee-validate/zod pg postgres uuid vee-validate
    ```

2. **Generated Directories:**

    - Delete `frontend/coverage/`, `frontend/dist/`, `frontend/playwright-report/`, `frontend/test-results/`
    - Add to .gitignore if not already present

3. **Redundant Files:**
    - Remove `frontend/server/db/schema.ts` (redundant with schema/index.ts)
    - Remove `frontend/components.json` (not using Shadcn-vue)
    - Remove `frontend/lib/utils.ts` (not imported anywhere)

### Medium Priority - Verify Before Removing

1. **Dependencies to Review:**

    ```bash
    # Consider removing if Header.vue component is not used:
    npm uninstall @vueuse/core

    # Verify these aren't used indirectly:
    npm uninstall clsx tailwind-merge @vitejs/plugin-vue playwright-core wait-on
    ```

2. **Files to Verify:**
    - Check if `components/Header.vue` is actually used (it imports @vueuse/core)
    - Verify `frontend/nuxt.config.e2e.ts` is needed

### Low Priority - Review and Clean

1. **Dev Dependencies:**

    - `@vitest/ui` - only used in scripts, consider if needed
    - `happy-dom` - verify it's configured properly for tests
    - `dotenv` - might be redundant with Nuxt's env handling

2. **Documentation:**
    - Review docs for accuracy and remove outdated content

## 💾 Estimated Space Savings

-   **Dependencies:** ~50-100MB in node_modules
-   **Generated files:** ~10-50MB
-   **Total:** Approximately 60-150MB

## ⚠️ Important Notes

-   Always test after removing dependencies
-   Some packages might be peer dependencies or used by other packages
-   Generated directories will be recreated during builds/tests
-   Keep backups before making changes

## 🧪 Testing After Cleanup

After removing unused items, run:

```bash
npm install
npm run build
npm run test
npm run test:e2e
```

## 🔍 Detailed Analysis

### Specific File Usage Analysis

**`frontend/lib/utils.ts`:**

-   Contains `clsx` utility function
-   Referenced in `frontend/components.json` aliases
-   **Status:** Potentially unused - no direct imports found in components

**`frontend/components.json`:**

-   Shadcn-vue configuration file
-   Project uses @nuxt/ui instead of Shadcn-vue
-   **Status:** Likely unused - can be removed

**`frontend/server/db/schema.ts`:**

-   Contains only `export * from './schema/auth';`
-   Redundant with `schema/index.ts` which exports both auth and app schemas
-   **Status:** Can be removed - redundant file

### Package Usage Deep Dive

**`clsx` package:**

-   Only imported in `lib/utils.ts`
-   `lib/utils.ts` doesn't appear to be used anywhere
-   **Recommendation:** Remove both the package and the file

**`@vueuse/core`:**

-   Common Vue composition utilities
-   Used in `components/Header.vue` for `createReusableTemplate` only
-   **Recommendation:** Keep if using Header component, otherwise remove

**`pg` and `postgres` packages:**

-   Traditional PostgreSQL drivers
-   Project uses `@neondatabase/serverless` instead
-   **Recommendation:** Safe to remove

**`uuid` package:**

-   UUID generation utility
-   Not found in any imports
-   Database uses text IDs but generation might be handled by Better Auth
-   **Recommendation:** Remove if not needed

### Configuration Files Analysis

**Multiple `.gitignore` files:**

-   Root `.gitignore`
-   `frontend/.gitignore`
-   **Recommendation:** Consolidate into single root `.gitignore`

**Docker configurations:**

-   `docker-compose.yml` - Development
-   `docker-compose.test.yml` - Testing
-   `docker-compose.production.yml` - Production
-   **Status:** All appear to be used

## 📋 Action Items Checklist

### Immediate Actions (Safe)

-   [ ] Remove unused production dependencies
-   [ ] Delete generated directories
-   [ ] Remove `frontend/components.json` if not using Shadcn-vue
-   [ ] Remove `frontend/lib/utils.ts` if unused

### Verification Needed

-   [ ] Check if `clsx` is used anywhere indirectly
-   [ ] Verify `@vueuse/core` isn't used in auto-imports
-   [ ] Confirm `uuid` isn't needed for any ID generation
-   [ ] Test that `@vitejs/plugin-vue` isn't required by Nuxt

### Optional Cleanup

-   [ ] Consolidate `.gitignore` files
-   [ ] Review and update documentation
-   [ ] Consider removing dev-only packages if not needed

## 🚀 Quick Start Cleanup Script

```bash
#!/bin/bash
# Quick cleanup script - run from project root

echo "🧹 Starting project cleanup..."

# Remove unused dependencies
echo "📦 Removing unused dependencies..."
cd frontend
npm uninstall @radix-icons/vue @vee-validate/zod pg postgres uuid vee-validate

# Remove generated directories
echo "🗂️ Removing generated directories..."
rm -rf coverage/ dist/ playwright-report/ test-results/

# Remove redundant files
echo "📄 Removing redundant files..."
rm -f components.json
rm -f lib/utils.ts
rm -f server/db/schema.ts

echo "✅ Cleanup complete! Run tests to verify everything works."
echo "🧪 Next steps:"
echo "  npm install"
echo "  npm run build"
echo "  npm run test"
```
